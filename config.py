"""
A.T.L.A.S Configuration - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated config.
"""

import os
import logging
from typing import Optional, Dict, Any

# Try to load dotenv, but don't fail if not available
try:
    from dotenv import load_dotenv
    load_dotenv()
    logger = logging.getLogger(__name__)
    logger.info("[CONFIG] Environment variables loaded from .env file")
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("[CONFIG] python-dotenv not available, using system environment variables")

# Use direct configuration implementation
logger.info("[CONFIG] Using direct configuration implementation")

# Direct configuration implementation
class Settings:
        """Atlas configuration settings loaded from environment variables"""
        
        def __init__(self):
            # Application Settings
            self.ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
            self.DEBUG = os.getenv("DEBUG", "True").lower() == "true"
            self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
            self.PORT = int(os.getenv("PORT", "8002"))
            
            # Validation Mode
            self.VALIDATION_MODE = os.getenv("VALIDATION_MODE", "False").lower() == "true"
            
            # API Keys
            self.ALPACA_API_KEY = os.getenv("ALPACA_API_KEY")
            self.ALPACA_SECRET_KEY = os.getenv("ALPACA_SECRET_KEY")
            self.ALPACA_BASE_URL = os.getenv("ALPACA_BASE_URL", "https://paper-api.alpaca.markets")
            
            self.FMP_API_KEY = os.getenv("FMP_API_KEY")
            self.FMP_BASE_URL = os.getenv("FMP_BASE_URL", "https://financialmodelingprep.com/api")
            
            # Grok AI Configuration
            self.GROK_API_KEY = os.getenv("GROK_API_KEY")
            self.GROK_BASE_URL = os.getenv("GROK_BASE_URL", "https://api.x.ai/v1")
            self.GROK_MODEL = os.getenv("GROK_MODEL", "grok-3-latest")
            self.GROK_TEMPERATURE = float(os.getenv("GROK_TEMPERATURE", "0.2"))
            
            # OpenAI Configuration
            self.OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
            self.OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4")
            self.OPENAI_TEMPERATURE = float(os.getenv("OPENAI_TEMPERATURE", "0.2"))
            
            # Database Configuration
            self.DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///atlas.db")
            self.MEMORY_DATABASE_URL = os.getenv("MEMORY_DATABASE_URL", "sqlite:///atlas_memory.db")
            
            # Trading Configuration
            self.MAX_POSITION_SIZE = float(os.getenv("MAX_POSITION_SIZE", "10000"))
            self.MAX_DAILY_TRADES = int(os.getenv("MAX_DAILY_TRADES", "10"))
            self.RISK_TOLERANCE = float(os.getenv("RISK_TOLERANCE", "0.02"))
            
            # Scanner Configuration
            self.SCANNER_INTERVAL = int(os.getenv("SCANNER_INTERVAL", "60"))
            self.MAX_SYMBOLS_PER_SCAN = int(os.getenv("MAX_SYMBOLS_PER_SCAN", "100"))

            # Search API Configuration
            self.GOOGLE_SEARCH_API_KEY = os.getenv("GOOGLE_SEARCH_API_KEY")
            self.GOOGLE_SEARCH_ENGINE_ID = os.getenv("GOOGLE_SEARCH_ENGINE_ID")
            self.BING_SEARCH_API_KEY = os.getenv("BING_SEARCH_API_KEY")

            # News API Configuration
            self.NEWS_API_KEY = os.getenv("NEWS_API_KEY")
            self.ALPHA_VANTAGE_API_KEY = os.getenv("ALPHA_VANTAGE_API_KEY")

            # Additional API Keys
            self.POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")
            self.QUANDL_API_KEY = os.getenv("QUANDL_API_KEY")

            # Web Search Configuration
            self.WEB_SEARCH_MARKET_ENABLED = os.getenv("WEB_SEARCH_MARKET_ENABLED", "True").lower() == "true"
            self.WEB_SEARCH_NEWS_ENABLED = os.getenv("WEB_SEARCH_NEWS_ENABLED", "True").lower() == "true"
            self.WEB_SEARCH_RISK_ENABLED = os.getenv("WEB_SEARCH_RISK_ENABLED", "True").lower() == "true"
            self.WEB_SEARCH_TRADING_ENABLED = os.getenv("WEB_SEARCH_TRADING_ENABLED", "True").lower() == "true"
            self.WEB_SEARCH_AI_ENABLED = os.getenv("WEB_SEARCH_AI_ENABLED", "True").lower() == "true"
            self.WEB_SEARCH_EDUCATION_ENABLED = os.getenv("WEB_SEARCH_EDUCATION_ENABLED", "True").lower() == "true"

            # News and Analytics Configuration
            self.NEWS_INSIGHTS_ENABLED = os.getenv("NEWS_INSIGHTS_ENABLED", "True").lower() == "true"
            self.SENTIMENT_ANALYSIS_ENABLED = os.getenv("SENTIMENT_ANALYSIS_ENABLED", "True").lower() == "true"

            logger.info("[CONFIG] Configuration loaded successfully")

# Create global settings instance
settings = Settings()

def get_api_config(service: Optional[str] = None) -> Dict[str, Any]:
        """Get API configuration for a specific service"""
        config = {
            "alpaca_api_key": settings.ALPACA_API_KEY,
            "alpaca_secret_key": settings.ALPACA_SECRET_KEY,
            "alpaca_base_url": settings.ALPACA_BASE_URL,
            "fmp_api_key": settings.FMP_API_KEY,
            "fmp_base_url": settings.FMP_BASE_URL,
            "grok_api_key": settings.GROK_API_KEY,
            "grok_base_url": settings.GROK_BASE_URL,
            "openai_api_key": settings.OPENAI_API_KEY,
        }
        
        if service:
            service_configs = {
                "alpaca": {
                    "api_key": settings.ALPACA_API_KEY,
                    "secret_key": settings.ALPACA_SECRET_KEY,
                    "base_url": settings.ALPACA_BASE_URL,
                    "available": bool(settings.ALPACA_API_KEY and settings.ALPACA_SECRET_KEY)
                },
                "fmp": {
                    "api_key": settings.FMP_API_KEY,
                    "base_url": settings.FMP_BASE_URL,
                    "available": bool(settings.FMP_API_KEY)
                },
                "grok": {
                    "api_key": settings.GROK_API_KEY,
                    "base_url": settings.GROK_BASE_URL,
                    "model": settings.GROK_MODEL,
                    "available": bool(settings.GROK_API_KEY)
                },
                "openai": {
                    "api_key": settings.OPENAI_API_KEY,
                    "model": settings.OPENAI_MODEL,
                    "available": bool(settings.OPENAI_API_KEY)
                }
            }
            return service_configs.get(service, {})
        
        return config

def validate_api_keys() -> Dict[str, bool]:
        """Validate that required API keys are present"""
        return {
            "alpaca": bool(settings.ALPACA_API_KEY and settings.ALPACA_SECRET_KEY),
            "fmp": bool(settings.FMP_API_KEY),
            "grok": bool(settings.GROK_API_KEY),
            "openai": bool(settings.OPENAI_API_KEY)
        }

def get_database_config() -> Dict[str, str]:
        """Get database configuration"""
        return {
            "database_url": settings.DATABASE_URL,
            "memory_database_url": settings.MEMORY_DATABASE_URL
        }

# Additional compatibility functions
def get_trading_config() -> Dict[str, Any]:
    """Get trading configuration"""
    return {
        "max_position_size": getattr(settings, 'MAX_POSITION_SIZE', 10000),
        "max_daily_trades": getattr(settings, 'MAX_DAILY_TRADES', 10),
        "risk_tolerance": getattr(settings, 'RISK_TOLERANCE', 0.02)
    }

def get_scanner_config() -> Dict[str, Any]:
    """Get scanner configuration"""
    return {
        "scanner_interval": getattr(settings, 'SCANNER_INTERVAL', 60),
        "max_symbols_per_scan": getattr(settings, 'MAX_SYMBOLS_PER_SCAN', 100)
    }

def is_production() -> bool:
    """Check if running in production mode"""
    return getattr(settings, 'ENVIRONMENT', 'development').lower() == 'production'

def is_debug() -> bool:
    """Check if debug mode is enabled"""
    return getattr(settings, 'DEBUG', True)

# Export main components for compatibility
__all__ = [
    'settings',
    'get_api_config',
    'validate_api_keys',
    'get_database_config',
    'get_trading_config',
    'get_scanner_config',
    'is_production',
    'is_debug'
]
