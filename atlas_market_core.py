"""
A.T.L.A.S. Market Core - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated market engine.
"""

import logging
import sys
import os

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'market'))

logger = logging.getLogger(__name__)

# Import the consolidated market engine implementation
try:
    from atlas_market_engine import (
        AtlasMarketEngine,
        MarketDataProvider,
        MarketStatus,
        Quote,
        MarketHours
    )
    
    logger.info("[BRIDGE] Successfully imported AtlasMarketEngine from consolidated engine")
    
except ImportError as e:
    logger.error(f"[BRIDGE] Failed to import from consolidated market engine: {e}")
    
    # Fallback implementation for critical functionality
    from datetime import datetime
    from typing import Dict, List, Optional, Any
    from dataclasses import dataclass
    from enum import Enum
    
    class MarketStatus(Enum):
        """Market status enumeration"""
        OPEN = "open"
        CLOSED = "closed"
        PRE_MARKET = "pre_market"
        AFTER_HOURS = "after_hours"
        UNKNOWN = "unknown"
    
    @dataclass
    class Quote:
        """Stock quote data structure"""
        symbol: str
        price: float
        change: float
        change_percent: float
        volume: int
        timestamp: datetime
        
    @dataclass
    class MarketHours:
        """Market hours information"""
        is_open: bool
        next_open: Optional[datetime]
        next_close: Optional[datetime]
        
    class MarketDataProvider:
        """Fallback market data provider"""
        
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.logger.warning("[FALLBACK] Using fallback MarketDataProvider")
        
        async def get_quote(self, symbol: str) -> Optional[Quote]:
            """Get quote for symbol"""
            self.logger.warning(f"[FALLBACK] Quote request for {symbol} - no data available")
            return None
        
        async def get_quotes(self, symbols: List[str]) -> Dict[str, Quote]:
            """Get quotes for multiple symbols"""
            self.logger.warning(f"[FALLBACK] Quotes request for {len(symbols)} symbols - no data available")
            return {}
    
    class AtlasMarketEngine:
        """Fallback implementation of AtlasMarketEngine"""
        
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.data_provider = MarketDataProvider()
            self.status = MarketStatus.UNKNOWN
            self.logger.warning("[FALLBACK] Using fallback AtlasMarketEngine implementation")
        
        async def initialize(self):
            """Initialize the market engine"""
            self.logger.info("[FALLBACK] Market engine initialized in fallback mode")
            return True
        
        async def get_quote(self, symbol: str) -> Optional[Quote]:
            """Get current quote for a symbol"""
            return await self.data_provider.get_quote(symbol)
        
        async def get_quotes(self, symbols: List[str]) -> Dict[str, Quote]:
            """Get quotes for multiple symbols"""
            return await self.data_provider.get_quotes(symbols)
        
        async def get_market_status(self) -> MarketStatus:
            """Get current market status"""
            return self.status
        
        async def get_market_hours(self) -> MarketHours:
            """Get market hours information"""
            return MarketHours(
                is_open=False,
                next_open=None,
                next_close=None
            )
        
        async def start_real_time_data(self, symbols: List[str]):
            """Start real-time data feed"""
            self.logger.info(f"[FALLBACK] Real-time data requested for {len(symbols)} symbols")
        
        async def stop_real_time_data(self):
            """Stop real-time data feed"""
            self.logger.info("[FALLBACK] Real-time data stopped")
        
        def get_engine_status(self) -> Dict[str, Any]:
            """Get engine status"""
            return {
                "status": "fallback",
                "mode": "fallback",
                "data_provider": "none",
                "real_time_active": False
            }

# Export the main classes for compatibility
__all__ = [
    'AtlasMarketEngine',
    'MarketDataProvider', 
    'MarketStatus',
    'Quote',
    'MarketHours'
]
